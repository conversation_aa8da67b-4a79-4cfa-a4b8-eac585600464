# Export Excel 应用配置
# 此配置文件应放置在 /etc/nginx/conf.d/ 目录下
server {
        listen 9999;
        server_name localhost;  # 替换为您的域名

        # 字符编码设置
        charset utf-8;

        # 分享链接路由 - 明确处理share路径
        location /share/ {
            root /opt/export_excel/frontend/dist;
            try_files $uri $uri/ /index.html;
            index index.html;

            # 防止缓存分享页面
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # 前端静态文件
        location / {
            root /opt/export_excel/frontend/dist;  # 前端构建文件路径
            try_files $uri $uri/ /index.html;  # 支持Vue路由
            index index.html;

            # 防止缓存HTML文件，确保前端更新能立即生效
            location ~* \.(html)$ {
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }

            # 静态资源缓存设置（JS、CSS文件有hash，可以长期缓存）
            location ~* \.(js|css)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }

        # 后端API代理 - 去掉/api前缀转发到后端
        location /api/ {
            # 重写URL，去掉/api前缀
            rewrite ^/api/(.*)$ /$1 break;
            proxy_pass http://127.0.0.1:5000;  # 后端Flask服务地址
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 代理超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            # 支持大文件上传
            client_max_body_size 100M;

            # 禁用缓存API响应
            proxy_cache_bypass 1;
            proxy_no_cache 1;
        }

        # Excel模板文件访问（允许查看目录和下载文件）
        location /excel_files/templates/ {
            alias /opt/export_excel/backend/excel_files/templates/;
            # 允许目录浏览
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
            # 支持中文文件名显示
            charset utf-8;

            # Excel文件下载配置
            location ~* \.(xlsx|xls)$ {
                # 设置正确的Content-Type
                add_header Content-Type "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                # 缓存设置
                expires 1d;
                add_header Cache-Control "public, immutable";
                # 支持中文文件名
                charset utf-8;
            }
        }

        # Excel生成文件访问（允许查看目录和下载文件）
        location /excel_files/generated/ {
            alias /opt/export_excel/backend/excel_files/generated/;
            # 允许目录浏览
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
            # 支持中文文件名显示
            charset utf-8;

            # Excel文件下载配置
            location ~* \.(xlsx|xls)$ {
                # 设置正确的Content-Type
                add_header Content-Type "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                # 缓存设置
                expires 1h;
                add_header Cache-Control "no-cache";
                # 支持中文文件名
                charset utf-8;
            }
        }

        # # 静态资源缓存设置
        # location ~* \.(jpg|jpeg|png|gif|ico)$ {
        #     expires 1d;
        #     add_header Cache-Control "public";
        # }

        # 错误页面
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
}