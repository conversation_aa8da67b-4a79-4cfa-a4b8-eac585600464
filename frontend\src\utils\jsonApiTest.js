/**
 * JSON API测试工具
 * 用于测试JSON导入API的连通性和功能
 */

export class JsonApiTester {
  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production' ? '' : 'http://localhost:5000'
  }

  /**
   * 测试JSON导入预览API
   */
  async testPreviewApi() {
    const testData = {
      "公司名称": "测试公司",
      "文档后缀": "安全监测",
      "记录日期": "2025-08-31",
      "客户标识": "TEST001",
      "服务器信息": [
        {
          "IP地址": "*************",
          "系统发行版": "CentOS 7",
          "CPU": "8核",
          "内存": "16GB",
          "磁盘": "500GB"
        }
      ]
    }

    try {
      console.log('🧪 开始测试JSON导入预览API...')
      console.log('📤 测试数据:', testData)

      const response = await fetch(`${this.baseUrl}/excel/import_json/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          json_data: JSON.stringify(testData)
        })
      })

      console.log('📡 响应状态:', response.status, response.statusText)
      console.log('📡 响应头:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API调用失败:', errorText)
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`,
          status: response.status
        }
      }

      const result = await response.json()
      console.log('✅ API调用成功:', result)

      return {
        success: true,
        data: result,
        status: response.status
      }

    } catch (error) {
      console.error('❌ 网络错误:', error)
      return {
        success: false,
        error: error.message,
        networkError: true
      }
    }
  }

  /**
   * 测试JSON导入确认API
   */
  async testConfirmApi(tempDataId) {
    if (!tempDataId) {
      return {
        success: false,
        error: '缺少临时数据ID'
      }
    }

    try {
      console.log('🧪 开始测试JSON导入确认API...')
      console.log('📤 临时数据ID:', tempDataId)

      const response = await fetch(`${this.baseUrl}/excel/import_json/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          temp_data_id: tempDataId
        })
      })

      console.log('📡 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API调用失败:', errorText)
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`,
          status: response.status
        }
      }

      const result = await response.json()
      console.log('✅ API调用成功:', result)

      return {
        success: true,
        data: result,
        status: response.status
      }

    } catch (error) {
      console.error('❌ 网络错误:', error)
      return {
        success: false,
        error: error.message,
        networkError: true
      }
    }
  }

  /**
   * 完整的JSON导入流程测试
   */
  async testFullFlow() {
    console.log('🚀 开始完整JSON导入流程测试...')

    // 第一步：测试预览API
    const previewResult = await this.testPreviewApi()
    if (!previewResult.success) {
      console.error('❌ 预览API测试失败:', previewResult.error)
      return {
        success: false,
        step: 'preview',
        error: previewResult.error
      }
    }

    console.log('✅ 预览API测试成功')

    // 检查预览结果
    if (previewResult.data.status !== 'success') {
      console.error('❌ 预览API返回错误:', previewResult.data.message)
      return {
        success: false,
        step: 'preview',
        error: previewResult.data.message
      }
    }

    // 获取临时数据ID
    const tempDataId = previewResult.data.data.temp_data_id || previewResult.data.data.temp_file_id
    if (!tempDataId) {
      console.error('❌ 预览API未返回临时数据ID')
      return {
        success: false,
        step: 'preview',
        error: '未获取到临时数据ID'
      }
    }

    console.log('📋 获取到临时数据ID:', tempDataId)

    // 第二步：测试确认API
    const confirmResult = await this.testConfirmApi(tempDataId)
    if (!confirmResult.success) {
      console.error('❌ 确认API测试失败:', confirmResult.error)
      return {
        success: false,
        step: 'confirm',
        error: confirmResult.error
      }
    }

    console.log('✅ 确认API测试成功')

    // 检查确认结果
    if (confirmResult.data.status !== 'success') {
      console.error('❌ 确认API返回错误:', confirmResult.data.message)
      return {
        success: false,
        step: 'confirm',
        error: confirmResult.data.message
      }
    }

    console.log('🎉 完整JSON导入流程测试成功！')
    return {
      success: true,
      previewData: previewResult.data,
      confirmData: confirmResult.data
    }
  }

  /**
   * 测试API连通性
   */
  async testConnectivity() {
    try {
      console.log('🔗 测试API连通性...')
      
      const response = await fetch(`${this.baseUrl}/excel/`, {
        method: 'GET'
      })

      console.log('📡 连通性测试响应:', response.status, response.statusText)

      if (response.ok) {
        const result = await response.json()
        console.log('✅ API连通性正常:', result)
        return { success: true, data: result }
      } else {
        const errorText = await response.text()
        console.error('❌ API连通性异常:', errorText)
        return { success: false, error: errorText }
      }

    } catch (error) {
      console.error('❌ 网络连接失败:', error)
      return { success: false, error: error.message, networkError: true }
    }
  }

  /**
   * 诊断JSON导入问题
   */
  async diagnose() {
    console.log('🔍 开始诊断JSON导入问题...')

    const results = {
      connectivity: null,
      preview: null,
      fullFlow: null,
      summary: {
        success: false,
        issues: [],
        suggestions: []
      }
    }

    // 1. 测试连通性
    console.log('\n1️⃣ 测试API连通性...')
    results.connectivity = await this.testConnectivity()
    
    if (!results.connectivity.success) {
      results.summary.issues.push('API连通性失败')
      if (results.connectivity.networkError) {
        results.summary.suggestions.push('检查后端服务是否启动')
        results.summary.suggestions.push('检查网络连接')
      } else {
        results.summary.suggestions.push('检查API路径配置')
      }
      return results
    }

    // 2. 测试预览API
    console.log('\n2️⃣ 测试预览API...')
    results.preview = await this.testPreviewApi()
    
    if (!results.preview.success) {
      results.summary.issues.push('预览API调用失败')
      if (results.preview.status === 405) {
        results.summary.suggestions.push('检查API路径是否正确')
        results.summary.suggestions.push('检查HTTP方法是否支持')
      } else if (results.preview.status === 400) {
        results.summary.suggestions.push('检查请求参数格式')
      } else if (results.preview.status === 500) {
        results.summary.suggestions.push('检查后端服务日志')
      }
      return results
    }

    // 3. 测试完整流程
    console.log('\n3️⃣ 测试完整导入流程...')
    results.fullFlow = await this.testFullFlow()
    
    if (results.fullFlow.success) {
      results.summary.success = true
      console.log('🎉 所有测试通过！JSON导入功能正常')
    } else {
      results.summary.issues.push(`完整流程在${results.fullFlow.step}步骤失败`)
      results.summary.suggestions.push('检查后端日志获取详细错误信息')
    }

    return results
  }
}

// 创建默认实例
export const jsonApiTester = new JsonApiTester()

// 便捷方法
export const testJsonImport = () => {
  return jsonApiTester.diagnose()
}

export const quickTestPreview = () => {
  return jsonApiTester.testPreviewApi()
}
