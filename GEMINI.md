# Project Overview: Export Excel Application

This project, named "梆梆安全-运维信息登记平台" (Export Excel Application), is a full-stack web application designed for managing and exporting operational information into Excel files. It features a robust backend built with Flask and a dynamic frontend powered by Vue.js, supported by MySQL for data persistence and Redis for caching.

## Key Features

*   **Dynamic Component Configuration:** All component configurations are stored in the database, allowing for flexible and real-time updates without code changes.
*   **RBAC Permission Management:** A comprehensive role-based access control system manages users, roles, permissions, and user groups.
*   **Form Snapshot:** Users can save and restore the state of forms, enhancing the user experience.
*   **Duplicate Submission Detection:** Intelligent detection prevents redundant data entries.
*   **Excel Generation & Management:** Supports generating Excel files from form data, managing templates, and tracking historical operations.
*   **Performance Optimization:** Integrates Redis for multi-layer caching (user permissions, component configurations, templates) to significantly boost system performance.

## Technology Stack

### Frontend
*   **Framework:** Vue.js 3.0+
*   **UI Library:** Element Plus
*   **HTTP Client:** Axios
*   **CSS Framework:** Bootstrap 5.0+

### Backend
*   **Web Framework:** Flask 2.3.3
*   **ORM:** Flask-SQLAlchemy 3.0.5 (with SQLAlchemy 1.4.46)
*   **Authentication:** Flask-JWT-Extended 4.5.3, Flask-Bcrypt 1.0.1
*   **Caching:** Flask-Caching 2.1.0, redis 5.0.1
*   **Data Processing:** pandas 2.1.4, openpyxl 3.1.2
*   **WSGI Server:** Gunicorn 21.2.0
*   **Database Driver:** PyMySQL 1.1.0

### Databases
*   **Primary Database:** MySQL 8.0+
*   **Cache Database:** Redis 6.0+

### Web Server
*   **Reverse Proxy/Static Files:** Nginx 1.18+

## Building and Running

This project uses `uv` for Python package management (recommended) and `npm` for Node.js package management.

### Backend (Python/Flask)

1.  **Navigate to Backend Directory:**
    ```bash
cd backend
```
2.  **Install `uv` (if not already installed):**
    *   **Linux/Mac:**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```
*   **Windows (PowerShell):**
```powershell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```
3.  **Install Dependencies and Create Virtual Environment (using `uv`):**
```bash
uv sync
```
(This command automatically creates a `.venv` virtual environment and installs all dependencies from `requirements.txt` and `pyproject.toml`.)

4.  **Activate Virtual Environment:**
    *   **Linux/Mac:**
```bash
source .venv/bin/activate
```
*   **Windows:**
```bash
.venv\Scripts\activate
```
5.  **Initialize Database:**
    *   **Development Environment:**
```bash
python init_db.py --env development
        # Or using the dedicated script:
        python init_dev_db.py
```
*   **Production Environment:**
```bash
python init_db.py --env production
```
(Ensure MySQL and Redis services are running and accessible as configured in `backend/config.py` or `.env` files.)

6.  **Run the Flask Application:**
    *   **Development Mode (with debug):**
```bash
export FLASK_ENV=development # Linux/Mac
        set FLASK_ENV=development    # Windows
        export FLASK_DEBUG=1         # Linux/Mac
        set FLASK_DEBUG=1            # Windows
        python run.py
```
(Access at `http://localhost:5000`)
    *   **Production Mode (using Gunicorn):**
```bash
# Ensure gunicorn is installed: uv pip install gunicorn
        gunicorn -w 4 -b 0.0.0.0:5000 run:app
```
### Frontend (Vue.js)

1.  **Navigate to Frontend Directory:**
```bash
cd frontend
```
2.  **Install Dependencies:**
```bash
npm install
```
3.  **Run in Development Mode:**
```bash
npm run serve
```
(Access at `http://localhost:8080`. This will proxy API requests to the backend as configured in `vue.config.js`.)

4.  **Build for Production:**
```bash
npm run build
```
(This generates static files in the `dist/` directory, ready for deployment with Nginx.)

## Deployment and Service Management

### Automated Deployment

The project includes an automated deployment script `deploy.sh` for streamlined deployments.

*   **Usage:**
```bash
chmod +x deploy.sh
    ./deploy.sh [options]
```
*   **Common Options:**
    *   `-e, --env {dev|prod}`: Specify deployment environment (default: `prod`).
    *   `-p, --port PORT`: Specify Nginx frontend port (default: `9999`).
    *   `-b, --backend PORT`: Specify backend Flask port (default: `5000`).
    *   `--skip-frontend`: Skip frontend build.
    *   `--skip-backend`: Skip backend installation.
    *   `--skip-nginx`: Skip Nginx configuration.
    *   `--auto-start`: Automatically start backend services.

*   **Example: Production deployment with auto-start:**
```bash
./deploy.sh --auto-start
```
### Service Management

The `service.sh` script provides convenient commands to manage the backend Flask application and Nginx.

*   **Usage:**
```bash
chmod +x service.sh
    ./service.sh {command}
```
*   **Backend Commands:**
    *   `start`: Start backend service.
    *   `stop`: Stop backend service.
    *   `restart`: Restart backend service.
    *   `status`: Check backend service status.
    *   `logs`: View backend service logs.
*   **Nginx Commands:**
    *   `nginx-start`: Start Nginx service.
    *   `nginx-stop`: Stop Nginx service.
    *   `nginx-restart`: Restart Nginx service.
    *   `nginx-status`: Check Nginx service status.
*   **Full Service Commands:**
    *   `full-start`: Start both backend and Nginx.
    *   `full-stop`: Stop both backend and Nginx.
    *   `full-restart`: Restart both backend and Nginx.
    *   `full-status`: Check status of both backend and Nginx.

### Nginx Configuration

The `nginx.conf` file (located in the project root) is designed to be copied to `/etc/nginx/conf.d/export_excel.conf` on the server. It handles:
*   Serving frontend static files from `frontend/dist`.
*   Proxying API requests (`/api/` and `/excel/`) to the Flask backend (default `http://127.0.0.1:5000`).
*   Serving Excel template and generated files from `backend/excel_files/`.
*   Includes caching headers and security configurations.

### CI/CD

A `Jenkinsfile` is provided, outlining a Jenkins pipeline for automated builds, testing, and deployments, leveraging the `deploy.sh` script.

## Development Conventions

*   **Code Formatting:** `black` (Python)
*   **Linting:** `flake8` (Python), `eslint` (JavaScript/Vue)
*   **Type Checking:** `mypy` (Python)
*   **Testing:** `pytest` (Python backend)
*   **Environment Variables:** Configuration is managed through `backend/config.py` and `.env` files for both frontend and backend, supporting environment-specific settings.
*   **Database Migrations:** The `backend/migrations` directory suggests the use of a migration tool (likely Flask-Migrate/Alembic) for database schema changes.
# Project Overview: Export Excel Application

This project, named "梆梆安全-运维信息登记平台" (Export Excel Application), is a full-stack web application designed for managing and exporting operational information into Excel files. It features a robust backend built with Flask and a dynamic frontend powered by Vue.js, supported by MySQL for data persistence and Redis for caching.

## Key Features

*   **Dynamic Component Configuration:** All component configurations are stored in the database, allowing for flexible and real-time updates without code changes.
*   **RBAC Permission Management:** A comprehensive role-based access control system manages users, roles, permissions, and user groups.
*   **Form Snapshot:** Users can save and restore the state of forms, enhancing the user experience.
*   **Duplicate Submission Detection:** Intelligent detection prevents redundant data entries.
*   **Excel Generation & Management:** Supports generating Excel files from form data, managing templates, and tracking historical operations.
*   **Performance Optimization:** Integrates Redis for multi-layer caching (user permissions, component configurations, templates) to significantly boost system performance.

## Technology Stack

### Frontend
*   **Framework:** Vue.js 3.0+
*   **UI Library:** Element Plus
*   **HTTP Client:** Axios
*   **CSS Framework:** Bootstrap 5.0+

### Backend
*   **Web Framework:** Flask 2.3.3
*   **ORM:** Flask-SQLAlchemy 3.0.5 (with SQLAlchemy 1.4.46)
*   **Authentication:** Flask-JWT-Extended 4.5.3, Flask-Bcrypt 1.0.1
*   **Caching:** Flask-Caching 2.1.0, redis 5.0.1
*   **Data Processing:** pandas 2.1.4, openpyxl 3.1.2
*   **WSGI Server:** Gunicorn 21.2.0
*   **Database Driver:** PyMySQL 1.1.0

### Databases
*   **Primary Database:** MySQL 8.0+
*   **Cache Database:** Redis 6.0+

### Web Server
*   **Reverse Proxy/Static Files:** Nginx 1.18+

## Building and Running

This project uses `uv` for Python package management (recommended) and `npm` for Node.js package management.

### Backend (Python/Flask)

1.  **Navigate to Backend Directory:**
```bash
cd backend
```

2.  **Install `uv` (if not already installed):**
    *   **Linux/Mac:**
        ```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```
    *   **Windows (PowerShell):**
        ```powershell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

3.  **Install Dependencies and Create Virtual Environment (using `uv`):**
    ```bash
uv sync
```
    (This command automatically creates a `.venv` virtual environment and installs all dependencies from `requirements.txt` and `pyproject.toml`.)

4.  **Activate Virtual Environment:**
    *   **Linux/Mac:**
        ```bash
source .venv/bin/activate
```
    *   **Windows:**
        ```bash
.venv\Scripts\activate
```

5.  **Initialize Database:**
    *   **Development Environment:**
        ```bash
python init_db.py --env development
        # Or using the dedicated script:
        python init_dev_db.py
```
    *   **Production Environment:**
        ```bash
python init_db.py --env production
```
    (Ensure MySQL and Redis services are running and accessible as configured in `backend/config.py` or `.env` files.)

6.  **Run the Flask Application:**
    *   **Development Mode (with debug):**
        ```bash
export FLASK_ENV=development # Linux/Mac
        set FLASK_ENV=development    # Windows
        export FLASK_DEBUG=1         # Linux/Mac
        set FLASK_DEBUG=1            # Windows
        python run.py
```
        (Access at `http://localhost:5000`)
    *   **Production Mode (using Gunicorn):**
        ```bash
# Ensure gunicorn is installed: uv pip install gunicorn
        gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

### Frontend (Vue.js)

1.  **Navigate to Frontend Directory:**
    ```bash
cd frontend
```

2.  **Install Dependencies:**
    ```bash
npm install
```

3.  **Run in Development Mode:**
    ```bash
npm run serve
```
    (Access at `http://localhost:8080`. This will proxy API requests to the backend as configured in `vue.config.js`.)

4.  **Build for Production:**
    ```bash
npm run build
```
    (This generates static files in the `dist/` directory, ready for deployment with Nginx.)

## Deployment and Service Management

### Automated Deployment

The project includes an automated deployment script `deploy.sh` for streamlined deployments.

*   **Usage:**
    ```bash
chmod +x deploy.sh
    ./deploy.sh [options]
```
*   **Common Options:**
    *   `-e, --env {dev|prod}`: Specify deployment environment (default: `prod`).
    *   `-p, --port PORT`: Specify Nginx frontend port (default: `9999`).
    *   `-b, --backend PORT`: Specify backend Flask port (default: `5000`).
    *   `--skip-frontend`: Skip frontend build.
    *   `--skip-backend`: Skip backend installation.
    *   `--skip-nginx`: Skip Nginx configuration.
    *   `--auto-start`: Automatically start backend services.

*   **Example: Production deployment with auto-start:**
    ```bash
./deploy.sh --auto-start
```

### Service Management

The `service.sh` script provides convenient commands to manage the backend Flask application and Nginx.

*   **Usage:**
    ```bash
chmod +x service.sh
    ./service.sh {command}
```
*   **Backend Commands:**
    *   `start`: Start backend service.
    *   `stop`: Stop backend service.
    *   `restart`: Restart backend service.
    *   `status`: Check backend service status.
    *   `logs`: View backend service logs.
*   **Nginx Commands:**
    *   `nginx-start`: Start Nginx service.
    *   `nginx-stop`: Stop Nginx service.
    *   `nginx-restart`: Restart Nginx service.
    *   `nginx-status`: Check Nginx service status.
*   **Full Service Commands:**
    *   `full-start`: Start both backend and Nginx.
    *   `full-stop`: Stop both backend and Nginx.
    *   `full-restart`: Restart both backend and Nginx.
    *   `full-status`: Check status of both backend and Nginx.

### Nginx Configuration

The `nginx.conf` file (located in the project root) is designed to be copied to `/etc/nginx/conf.d/export_excel.conf` on the server. It handles:
*   Serving frontend static files from `frontend/dist`.
*   Proxying API requests (`/api/` and `/excel/`) to the Flask backend (default `http://127.0.0.1:5000`).
*   Serving Excel template and generated files from `backend/excel_files/`.
*   Includes caching headers and security configurations.

### CI/CD

A `Jenkinsfile` is provided, outlining a Jenkins pipeline for automated builds, testing, and deployments, leveraging the `deploy.sh` script.

## Development Conventions

*   **Code Formatting:** `black` (Python)
*   **Linting:** `flake8` (Python), `eslint` (JavaScript/Vue)
*   **Type Checking:** `mypy` (Python)
*   **Testing:** `pytest` (Python backend)
*   **Environment Variables:** Configuration is managed through `backend/config.py` and `.env` files for both frontend and backend, supporting environment-specific settings.
*   **Database Migrations:** The `backend/migrations` directory suggests the use of a migration tool (likely Flask-Migrate/Alembic) for database schema changes.
# Project Overview: Export Excel Application

This project, named "梆梆安全-运维信息登记平台" (Export Excel Application), is a full-stack web application designed for managing and exporting operational information into Excel files. It features a robust backend built with Flask and a dynamic frontend powered by Vue.js, supported by MySQL for data persistence and Redis for caching.

## Key Features

*   **Dynamic Component Configuration:** All component configurations are stored in the database, allowing for flexible and real-time updates without code changes.
*   **RBAC Permission Management:** A comprehensive role-based access control system manages users, roles, permissions, and user groups.
*   **Form Snapshot:** Users can save and restore the state of forms, enhancing the user experience.
*   **Duplicate Submission Detection:** Intelligent detection prevents redundant data entries.
*   **Excel Generation & Management:** Supports generating Excel files from form data, managing templates, and tracking historical operations.
*   **Performance Optimization:** Integrates Redis for multi-layer caching (user permissions, component configurations, templates) to significantly boost system performance.

## Technology Stack

### Frontend
*   **Framework:** Vue.js 3.0+
*   **UI Library:** Element Plus
*   **HTTP Client:** Axios
*   **CSS Framework:** Bootstrap 5.0+

### Backend
*   **Web Framework:** Flask 2.3.3
*   **ORM:** Flask-SQLAlchemy 3.0.5 (with SQLAlchemy 1.4.46)
*   **Authentication:** Flask-JWT-Extended 4.5.3, Flask-Bcrypt 1.0.1
*   **Caching:** Flask-Caching 2.1.0, redis 5.0.1
*   **Data Processing:** pandas 2.1.4, openpyxl 3.1.2
*   **WSGI Server:** Gunicorn 21.2.0
*   **Database Driver:** PyMySQL 1.1.0

### Databases
*   **Primary Database:** MySQL 8.0+
*   **Cache Database:** Redis 6.0+

### Web Server
*   **Reverse Proxy/Static Files:** Nginx 1.18+

## Building and Running

This project uses `uv` for Python package management (recommended) and `npm` for Node.js package management.

### Backend (Python/Flask)

1.  **Navigate to Backend Directory:**
    ```bash
    cd backend
    ```

2.  **Install `uv` (if not already installed):**
    *   **Linux/Mac:**
        ```bash
        curl -LsSf https://astral.sh/uv/install.sh | sh
        ```
    *   **Windows (PowerShell):**
        ```powershell
        powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
        ```

3.  **Install Dependencies and Create Virtual Environment (using `uv`):**
    ```bash
    uv sync
    ```
    (This command automatically creates a `.venv` virtual environment and installs all dependencies from `requirements.txt` and `pyproject.toml`.)

4.  **Activate Virtual Environment:**
    *   **Linux/Mac:**
        ```bash
        source .venv/bin/activate
        ```
    *   **Windows:**
        ```bash
        .venv\Scripts\activate
        ```

5.  **Initialize Database:**
    *   **Development Environment:**
        ```bash
        python init_db.py --env development
        # Or using the dedicated script:
        python init_dev_db.py
        ```
    *   **Production Environment:**
        ```bash
        python init_db.py --env production
        ```
    (Ensure MySQL and Redis services are running and accessible as configured in `backend/config.py` or `.env` files.)

6.  **Run the Flask Application:**
    *   **Development Mode (with debug):**
        ```bash
        export FLASK_ENV=development # Linux/Mac
        set FLASK_ENV=development    # Windows
        export FLASK_DEBUG=1         # Linux/Mac
        set FLASK_DEBUG=1            # Windows
        python run.py
        ```
        (Access at `http://localhost:5000`)
    *   **Production Mode (using Gunicorn):**
        ```bash
        # Ensure gunicorn is installed: uv pip install gunicorn
        gunicorn -w 4 -b 0.0.0.0:5000 run:app
        ```

### Frontend (Vue.js)

1.  **Navigate to Frontend Directory:**
    ```bash
    cd frontend
    ```

2.  **Install Dependencies:**
    ```bash
    npm install
    ```

3.  **Run in Development Mode:**
    ```bash
    npm run serve
    ```
    (Access at `http://localhost:8080`. This will proxy API requests to the backend as configured in `vue.config.js`.)

4.  **Build for Production:**
    ```bash
    npm run build
    ```
    (This generates static files in the `dist/` directory, ready for deployment with Nginx.)

## Deployment and Service Management

### Automated Deployment

The project includes an automated deployment script `deploy.sh` for streamlined deployments.

*   **Usage:**
    ```bash
    chmod +x deploy.sh
    ./deploy.sh [options]
    ```
*   **Common Options:**
    *   `-e, --env {dev|prod}`: Specify deployment environment (default: `prod`).
    *   `-p, --port PORT`: Specify Nginx frontend port (default: `9999`).
    *   `-b, --backend PORT`: Specify backend Flask port (default: `5000`).
    *   `--skip-frontend`: Skip frontend build.
    *   `--skip-backend`: Skip backend installation.
    *   `--skip-nginx`: Skip Nginx configuration.
    *   `--auto-start`: Automatically start backend services.

*   **Example: Production deployment with auto-start:**
    ```bash
    ./deploy.sh --auto-start
    ```

### Service Management

The `service.sh` script provides convenient commands to manage the backend Flask application and Nginx.

*   **Usage:**
    ```bash
    chmod +x service.sh
    ./service.sh {command}
    ```
*   **Backend Commands:**
    *   `start`: Start backend service.
    *   `stop`: Stop backend service.
    *   `restart`: Restart backend service.
    *   `status`: Check backend service status.
    *   `logs`: View backend service logs.
*   **Nginx Commands:**
    *   `nginx-start`: Start Nginx service.
    *   `nginx-stop`: Stop Nginx service.
    *   `nginx-restart`: Restart Nginx service.
    *   `nginx-status`: Check Nginx service status.
*   **Full Service Commands:**
    *   `full-start`: Start both backend and Nginx.
    *   `full-stop`: Stop both backend and Nginx.
    *   `full-restart`: Restart both backend and Nginx.
    *   `full-status`: Check status of both backend and Nginx.

### Nginx Configuration

The `nginx.conf` file (located in the project root) is designed to be copied to `/etc/nginx/conf.d/export_excel.conf` on the server. It handles:
*   Serving frontend static files from `frontend/dist`.
*   Proxying API requests (`/api/` and `/excel/`) to the Flask backend (default `http://127.0.0.1:5000`).
*   Serving Excel template and generated files from `backend/excel_files/`.
*   Includes caching headers and security configurations.

### CI/CD

A `Jenkinsfile` is provided, outlining a Jenkins pipeline for automated builds, testing, and deployments, leveraging the `deploy.sh` script.

## Development Conventions

*   **Code Formatting:** `black` (Python)
*   **Linting:** `flake8` (Python), `eslint` (JavaScript/Vue)
*   **Type Checking:** `mypy` (Python)
*   **Testing:** `pytest` (Python backend)
*   **Environment Variables:** Configuration is managed through `backend/config.py` and `.env` files for both frontend and backend, supporting environment-specific settings.
*   **Database Migrations:** The `backend/migrations` directory suggests the use of a migration tool (likely Flask-Migrate/Alembic) for database schema changes.
