<template>
  <div class="access-info-edit-test p-4">
    <div class="container">
      <h2 class="mb-4">
        <i class="bi bi-bug me-2"></i>
        访问信息手动编辑测试
      </h2>
      
      <!-- 测试说明 -->
      <div class="alert alert-info mb-4">
        <h5><i class="bi bi-info-circle me-2"></i>测试说明</h5>
        <ol class="mb-0">
          <li>点击"添加测试服务器"按钮添加服务器信息</li>
          <li>观察访问信息字段是否自动填充（显示浅灰色背景）</li>
          <li>点击输入框并手动修改内容</li>
          <li>再次添加服务器，验证手动修改的内容是否被保留</li>
          <li>点击"重置"按钮可重新启用自动填充</li>
        </ol>
      </div>

      <!-- 控制面板 -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="bi bi-gear me-2"></i>测试控制
          </h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <label class="form-label">表单类型</label>
              <select v-model="selectedFormType" class="form-select">
                <option value="安全测评">安全测评</option>
                <option value="安全监测">安全监测</option>
                <option value="应用加固">应用加固</option>
              </select>
            </div>
            <div class="col-md-8">
              <label class="form-label">操作</label>
              <div class="d-flex gap-2">
                <button class="btn btn-primary" @click="addTestServer">
                  <i class="bi bi-plus-circle me-1"></i>添加测试服务器
                </button>
                <button class="btn btn-warning" @click="clearServers">
                  <i class="bi bi-trash me-1"></i>清空服务器
                </button>
                <button class="btn btn-info" @click="showDebugInfo = !showDebugInfo">
                  <i class="bi bi-bug me-1"></i>调试信息
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 调试信息 -->
      <div v-if="showDebugInfo" class="card mb-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="bi bi-terminal me-2"></i>调试信息
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>服务器信息 ({{ testFormData.服务器信息.length }})</h6>
              <pre class="bg-light p-2 rounded small">{{ JSON.stringify(testFormData.服务器信息, null, 2) }}</pre>
            </div>
            <div class="col-md-6">
              <h6>访问信息数据</h6>
              <pre class="bg-light p-2 rounded small">{{ JSON.stringify(accessInfoData, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试表单 -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="bi bi-form me-2"></i>测试表单 - {{ selectedFormType }}
          </h5>
        </div>
        <div class="card-body">
          <dynamic-access-info-section
            v-model="accessInfoData"
            :field-config="accessFieldConfig"
            :form-type="selectedFormType"
            :server-list="testFormData.服务器信息"
            :key="refreshKey"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicAccessInfoSection from '@/components/forms/common/DynamicAccessInfoSection.vue'
import { getFormFieldConfig } from '@/config/formFieldConfig'
import { getInitialFormData } from '@/config/formDataConfig'

export default {
  name: 'AccessInfoEditTest',
  components: {
    DynamicAccessInfoSection
  },
  data() {
    return {
      selectedFormType: '安全监测',
      testFormData: {
        服务器信息: []
      },
      accessInfoData: {},
      refreshKey: 0,
      showDebugInfo: false
    }
  },
  computed: {
    accessFieldConfig() {
      const config = getFormFieldConfig(this.selectedFormType)
      return config.access || {}
    }
  },
  watch: {
    selectedFormType(newType) {
      this.testFormData = getInitialFormData(newType)
      this.accessInfoData = {}
      this.refreshKey += 1
      console.log('切换表单类型:', newType)
    }
  },
  methods: {
    addTestServer() {
      const server = {
        序号: this.testFormData.服务器信息.length + 1,
        IP地址: '',
        部署应用: [],
        组件端口: {}
      }

      // 根据表单类型添加不同的测试数据
      if (this.selectedFormType === '安全测评') {
        server.IP地址 = `10.27.193.${130 + this.testFormData.服务器信息.length}`
        server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'backend-ssp-user', 'luna']
        server.组件端口 = {
          'front-ssp-admin': '8080',
          'front-ssp-user': '8081',
          'backend-ssp-user': '8082',
          'luna': '9001'
        }
      } else if (this.selectedFormType === '安全监测') {
        server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`
        server.部署应用 = ['web-service-nginx', 'init', 'kibana']
        server.组件端口 = {
          'web-service-nginx': '443',
          'init': '8080',
          'kibana': '5601'
        }
      } else if (this.selectedFormType === '应用加固') {
        server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`
        server.部署应用 = ['secweb', 'luna']
        server.组件端口 = {
          'secweb': '8000',
          'luna': '9001'
        }
      }

      this.testFormData.服务器信息.push(server)
      console.log('添加测试服务器:', server)
    },

    clearServers() {
      this.testFormData.服务器信息 = []
      this.accessInfoData = {}
      this.refreshKey += 1
      console.log('清空服务器信息')
    }
  },
  mounted() {
    console.log('AccessInfoEditTest 组件已挂载')
    this.testFormData = getInitialFormData(this.selectedFormType)
  }
}
</script>

<style scoped>
.access-info-edit-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.8rem;
}

.alert {
  border: none;
  border-left: 4px solid #0dcaf0;
}
</style>
