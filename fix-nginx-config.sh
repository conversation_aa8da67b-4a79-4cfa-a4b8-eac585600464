#!/bin/bash

# 修复nginx配置脚本
# 用于解决JSON导入API 405错误问题

echo "🔧 开始修复nginx配置..."

# 检查后端服务状态
echo "1️⃣ 检查后端服务状态..."
if sudo systemctl is-active --quiet export-excel-backend; then
    echo "✅ 后端服务正在运行"
else
    echo "❌ 后端服务未运行，尝试启动..."
    sudo systemctl start export-excel-backend
    sleep 5
    if sudo systemctl is-active --quiet export-excel-backend; then
        echo "✅ 后端服务启动成功"
    else
        echo "❌ 后端服务启动失败，查看日志："
        sudo journalctl -u export-excel-backend --lines=20 --no-pager
        exit 1
    fi
fi

# 检查端口监听
echo "2️⃣ 检查端口监听..."
if netstat -tuln | grep -q ":5000 "; then
    echo "✅ 后端端口5000监听正常"
else
    echo "❌ 后端端口5000未监听"
    echo "检查进程："
    ps aux | grep gunicorn | grep -v grep
    exit 1
fi

# 备份现有nginx配置
echo "3️⃣ 备份现有nginx配置..."
if [ -f "/etc/nginx/conf.d/export-excel.conf" ]; then
    sudo cp /etc/nginx/conf.d/export-excel.conf /etc/nginx/conf.d/export-excel.conf.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 配置已备份"
else
    echo "⚠️ nginx配置文件不存在"
fi

# 创建修复后的nginx配置
echo "4️⃣ 创建修复后的nginx配置..."
sudo tee /etc/nginx/conf.d/export-excel.conf > /dev/null << 'EOF'
# Export Excel 应用配置 - 修复版本
server {
    listen 9999;
    server_name localhost;
    charset utf-8;

    # 前端静态文件
    location / {
        root /opt/export_excel/current/frontend-dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # 防止缓存HTML文件
        location ~* \.(html)$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # 静态资源缓存
        location ~* \.(js|css)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端API代理 - 修复版本
    location /api/ {
        # 重写URL，去掉/api前缀
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 代理超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 支持大文件上传
        client_max_body_size 100M;

        # 禁用缓存API响应
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        
        # 添加调试头
        add_header X-Debug-Proxy "API-Proxy" always;
    }

    # Excel文件访问
    location /excel_files/templates/ {
        alias /opt/export_excel/current/backend/excel_files/templates/;
        autoindex on;
        autoindex_exact_size off;
        autoindex_localtime on;
        charset utf-8;
    }

    location /excel_files/generated/ {
        alias /opt/export_excel/current/backend/excel_files/generated/;
        autoindex on;
        autoindex_exact_size off;
        autoindex_localtime on;
        charset utf-8;
    }

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
EOF

# 测试nginx配置
echo "5️⃣ 测试nginx配置..."
if sudo nginx -t; then
    echo "✅ nginx配置测试通过"
else
    echo "❌ nginx配置测试失败"
    exit 1
fi

# 重新加载nginx
echo "6️⃣ 重新加载nginx..."
if sudo systemctl reload nginx; then
    echo "✅ nginx重新加载成功"
else
    echo "❌ nginx重新加载失败"
    sudo systemctl status nginx --no-pager -l
    exit 1
fi

# 验证服务状态
echo "7️⃣ 验证服务状态..."
sleep 3

# 检查nginx状态
if sudo systemctl is-active --quiet nginx; then
    echo "✅ nginx服务运行正常"
else
    echo "❌ nginx服务异常"
    sudo systemctl status nginx --no-pager -l
fi

# 检查端口监听
if netstat -tuln | grep -q ":9999 "; then
    echo "✅ nginx端口9999监听正常"
else
    echo "❌ nginx端口9999未监听"
fi

# 测试API连通性
echo "8️⃣ 测试API连通性..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:9999/api/excel/ | grep -q "200"; then
    echo "✅ API连通性测试通过"
else
    echo "⚠️ API连通性测试失败，但这可能是正常的（如果后端没有根路由）"
fi

echo ""
echo "🎉 nginx配置修复完成！"
echo ""
echo "📋 服务信息："
echo "  前端访问: http://************:9999"
echo "  后端端口: 5000"
echo "  nginx端口: 9999"
echo ""
echo "🧪 测试建议："
echo "1. 访问 http://************:9999/json-import"
echo "2. 点击'测试API'按钮验证功能"
echo "3. 如果仍有问题，查看日志："
echo "   sudo tail -f /var/log/nginx/error.log"
echo "   sudo journalctl -u export-excel-backend -f"
